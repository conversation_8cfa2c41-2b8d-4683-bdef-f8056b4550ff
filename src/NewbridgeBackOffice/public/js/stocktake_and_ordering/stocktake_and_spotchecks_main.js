let CurrentHeaderAdd = 0;
let productAddCount = 0;
let productAddCurrent = 0;
$('body').on('click', '.add-product-button', function (e) {
    e.preventDefault();

    var ref = $('#product_data').val();

    productAddCount = _.size(ref);
    productAddCurrent = 0;
    if (productAddCount == 0) {
        return;
    }
    loading('#ProductArea', 'Products loading, please wait');
    newRow('product', ref);


    $('#product-dropdown').multiselect('refresh');

})

$('body').on('click', '.add-supplier-button', function (e) {
    e.preventDefault();
    var ref = $('#supplier_data').val();

    productAddCount = countSelectedProducts(ref, 'supplier_guid');
    productAddCurrent = 0;

    if (productAddCount == 0) {
        return;
    }
    loading('#ProductArea', 'Products loading, please wait');

    newRow('supplier', ref);

    clearSelection('suppliers');

    $('#supplier-dropdown option:selected').each(function () {
        $(this).prop('selected', false);
    })
    $('#supplier-dropdown').multiselect('refresh');
})

$('body').on('click', '.add-department-button', function (e) {
    e.preventDefault();
    var ref = $('#department_data').val();

    productAddCount = countSelectedProducts(ref, 'department_guid');
    productAddCurrent = 0;

    if (productAddCount == 0) {
        return;
    }
    loading('#ProductArea', 'Products loading, please wait');

    newRow('department', ref);

    clearSelection('departments');

    $('#department-dropdown option:selected').each(function () {
        $(this).prop('selected', false);
    })
    $('#department-dropdown').multiselect('refresh');
})

$('body').on('click', '.add-sub-department-button', function (e) {
    e.preventDefault();

    loading('#ProductArea', 'Products loading, please wait');

    var ref = $('#subdepartment_data').val();

    productAddCount = countSelectedProducts(ref, 'sub_department_guid');
    productAddCurrent = 0;

    if (productAddCount == 0) {
        return;
    }

    newRow('subdepartment', ref);

    clearSelection('subdepartments');

    $('#subdepartment-dropdown option:selected').each(function () {
        $(this).prop('selected', false);
    })
    $('#subdepartment-dropdown').multiselect('refresh');

});

function countSelectedProducts(ref, keyColumnName) {
    var count = 0;
    _.forEach(ref, function (value) {
        var itemData = _.filter(products, {[keyColumnName]: value});
        count += _.size(itemData);
    });
    return count;
}

function clearSelection(divClass) {
    var node = $('.' + divClass);
    $(node).find('ul li.active').each(function () {
        $(this).removeClass('active');
        $(this).find('input').each(function () {
            $(this).prop('checked', false);
        });
    });
    $(node).find('button.multiselect').prop('title', 'None selected');
    ;
    $(node).find('span.multiselect-selected-text').text('None selected');
}

$('body').on('click', '.add-allproducts-button', function (e) {
    e.preventDefault();
    loading('#ProductArea', 'Products loading, please wait...');
    newRow('allproducts', null);
})

var row = 0;
const arrayToSearch = ['stocktake', 'spotchecks'];
let currentProductAdd = 1;


let xitemList = [];
let xitemCount = 0;
let xcurrentItem = 0;

let productIndex = 100;
let categoryIndex = 100;

function newRow(type, ref) {
    xitemList = [];
    xitemCount = 0;
    xcurrentItem = 0;

    if (ref.length === 0) {
        notificationBar('error', 'Please choose a product, department or supplier to add to your order');
    }

    let keyColumnName = 'guid';
    switch (type) {
        case 'department':
            keyColumnName = 'department_guid'
            break;
        case 'subdepartment':
            keyColumnName = 'sub_department_guid'
            break;
        case 'supplier':
            keyColumnName = 'supplier_guid'
            break;
        default:
            keyColumnName = 'guid';
            break;
    }

    _.each(ref, function (guid, index) {
        let productList = _.filter(products, {[keyColumnName]: guid});
        productList = _.sortBy(productList, 'displayname');
        _.each(productList, function(product, oin){
            product.priority = categoryIndex+'.'+productIndex;
            productIndex++;
            xitemList.push(product);
        })
        categoryIndex = categoryIndex+100;
    })

    xitemCount = xitemList.length
    processAddRows()
    row++
}

function processAddRows() {

    let product = xitemList[xcurrentItem];

    let rowExists = $('tr[data-product="' + product.id + '"]').length;
    if (product.sku_guid != null && rowExists === 0) {

        createRow(product);
        updateRow(product);
        xcurrentItem++;
        if (xcurrentItem >= xitemCount) {
            $('#loading').remove();
            xcurrentItem = 0;
            xitemList = [];
            $('.createButtons').removeAttr('disabled');
            if(module !== 'stocktake') {
                reOrderProducts()
            }
        } else {
            processAddRows()
        }

    } else {
        xcurrentItem++;
        if (xcurrentItem >= xitemCount) {
            $('#loading').remove()
            xcurrentItem = 0;
            xitemList = [];
            $('.createButtons').removeAttr('disabled');
            if(module !== 'stocktake') {
                reOrderProducts()
            }
        } else {
            processAddRows()
        }
    }

}

function reOrderProducts() {
    let $wrapper = $('#OrderTable tbody');

    $wrapper.find("tr[data-priority!='0.00']").sort(function (a, b) {
        if (typeof a.split === "function") {
            let aParts = a.split(".", 2);
            let bParts = b.split(".", 2);
            if (aParts[0] < bParts[0]) return -1;
            if (aParts[0] > bParts[0]) return 1;
            return aParts[1] - bParts[1];
        }
    }).appendTo($wrapper);
}

reOrderAlphabetically = function () {
    // let $wrapper = $('#OrderTable tbody');
    //
    // $wrapper.find("tr").sort(function (a, b) {
    //     return $(a).find('.displayname').val().localeCompare($(b).find('.displayname').val());
    // }).appendTo($wrapper);
}

$('body').on('keyup, change', '.quantity', function (e) {
    if (e.which !== 9 || e.which !== 13) {

        var costprice = $(this).data('price');
        var quantity = $(this).val();
        var id = $(this).data('product');
        var stockQuantity = $('[data-product="' + id + '"].sku option:selected').data('qty');
        var units = (stockQuantity * quantity);

        // current stock value as shown in the view
        var current_stock = $('[data-product="' + id + '"].current_stock').val();

        if (arrayToSearch.includes(module)) {// Calculate the variance between the current and counted
            var variance = difference(parseFloat(current_stock), parseFloat(units))

            if (variance < 0) {
                $('[data-product="' + id + '"].variance').addClass('negative')
                $('[data-product="' + id + '"].variance').removeClass('positive')
                $('[data-product="' + id + '"].variance').removeClass('neutral')
            }
            if (variance > 0) {
                $('[data-product="' + id + '"].variance').addClass('positive')
                $('[data-product="' + id + '"].variance').removeClass('negative')
                $('[data-product="' + id + '"].variance').removeClass('neutral')
            }
            if (variance == 0) {
                $('[data-product="' + id + '"].variance').addClass('neutral')
                $('[data-product="' + id + '"].variance').removeClass('positive')
                $('[data-product="' + id + '"].variance').removeClass('negative')
            }

            var varianceTotal = variance > 0 ? Math.abs(variance * costprice) : (Math.abs(variance * costprice) * -1);
            $('[data-product="' + id + '"].variance').val(variance)
            $('[data-product="' + id + '"].variance_total').val(varianceTotal.toFixed(2))
        }

        var units = (stockQuantity * quantity);

        $('[data-product="' + id + '"].unitQuantity').val(units.toFixed(2))
    }
});

$('body').on('keyup, change', '.unitQuantity', function (e) {
    if (e.which !== 9 || e.which !== 13) {

        var costprice = $(this).data('price');
        var quantity = parseFloat($(this).val());
        var id = $(this).data('product');
        var stockQuantity = $('[data-product="' + id + '"].sku option:selected').data('qty');
        var stockUnits = (quantity / stockQuantity);
        var total = (costprice * quantity);

        // current stock value as shown in the view
        var current_stock = $('[data-product="' + id + '"].current_stock').val();

        if (arrayToSearch.includes(module)) {   // Calculate the variance between the current and counted
            var variance = difference(current_stock, quantity)
            var varianceTotal = variance > 0 ? parseFloat(Math.abs(variance * costprice)) : parseFloat((Math.abs(variance * costprice) * -1).toFixed(2));

            if (variance < 0) {
                $('[data-product="' + id + '"].variance').addClass('negative')
                $('[data-product="' + id + '"].variance').removeClass('positive')
                $('[data-product="' + id + '"].variance').removeClass('neutral')
            }
            if (variance > 0) {
                $('[data-product="' + id + '"].variance').addClass('positive')
                $('[data-product="' + id + '"].variance').removeClass('negative')
                $('[data-product="' + id + '"].variance').removeClass('neutral')
            }

            if (variance == 0) {
                $('[data-product="' + id + '"].variance').addClass('neutral')
                $('[data-product="' + id + '"].variance').removeClass('positive')
                $('[data-product="' + id + '"].variance').removeClass('negative')
            }

            $('[data-product="' + id + '"].variance').val(variance.toFixed(2))
            $('[data-product="' + id + '"].variance_total').val(varianceTotal.toFixed(2))
        }

        $('[data-product="' + id + '"].quantity').val(stockUnits.toFixed(2));
    }
})

function updateRow(itemData) {
    const current_stock = itemData.current_stock != null ? itemData.current_stock : 0;
    const variance = (current_stock * -1);
    const varianceTotal = variance > 0 ? Math.abs(variance * itemData.costprice) : (Math.abs(variance * itemData.costprice) * -1);

    var tr = $(`tr.item-record[data-product='${itemData.id}']`);
    if (tr) {
        tr.find('.current_stock').val(current_stock);
        tr.find('.variance_total').val(varianceTotal.toFixed(2));
        tr.find('.variance')
            .val(current_stock * -1)
            .removeClass('negative')
            .removeClass('positive')
            .removeClass('neutral')
            .addClass(variance < 0 ? 'negative' : variance > 0 ? 'positive' : 'neutral');
    }
}

function createRow(itemData) {
    var $tr = $('tbody tr').first();
    var $clone = $tr.clone();

    // Check if this is autosave data (has basic field values) or full product data
    var isAutosaveData = itemData.hasOwnProperty('name') && !itemData.hasOwnProperty('displayname');

    var current_stock = itemData.current_stock != null ? itemData.current_stock : 0;

    // Handle different data structures for price calculation
    var skuPrice, variance, varianceTotal, costprice;

    if (isAutosaveData) {
        // For autosave data, use the saved sku_price or default to 0
        skuPrice = itemData.sku_price ? parseFloat(itemData.sku_price) : 0;
        costprice = skuPrice; // Use sku_price as costprice for autosave data
    } else {
        // For full product data, calculate as before
        skuPrice = (itemData.qty * itemData.costprice).toFixed(2);
        costprice = itemData.costprice;
    }

    variance = (current_stock * -1);
    varianceTotal = variance > 0 ? Math.abs(variance * costprice) : (Math.abs(variance * costprice) * -1);

    // Set priority (may not exist in autosave data)
    if (itemData.priority) {
        $clone.attr('data-priority', itemData.priority);
        $clone.find('.priority').val(itemData.priority);
    }

    $clone.removeClass('copy-example');
    $clone.addClass('item-record');

    // Handle PLU field - use appropriate field based on data type
    var pluValue = isAutosaveData ? itemData.plu : itemData.id;
    $clone.find('.plu').val(pluValue);

    // Handle display name - construct differently based on data type
    var displayName;
    if (isAutosaveData) {
        // For autosave data, use the saved name directly
        displayName = itemData.name || '';
    } else {
        // For full product data, construct as before
        displayName = itemData.displayname + ' (' + itemData.sku.displayname + ')';
    }
    $clone.find('.displayname').val(displayName);

    $clone.find('input').attr('id', 'r' + row);
    $clone.find('.min_stock').val(itemData.min_stock);
    $clone.find('.max_stock').val(itemData.max_stock);
    $clone.find('.current_stock').val(current_stock);
    $clone.find('.skuPrice').val(typeof skuPrice === 'number' ? skuPrice.toFixed(2) : skuPrice);

    // Use appropriate ID field based on data type
    var productId = isAutosaveData ? (itemData.product_id || itemData.id) : itemData.id;
    $clone.attr('data-product', productId);
    $clone.find('input, select, button').attr('data-product', productId);
    $clone.find('.quantity').attr('data-price', costprice);
    $clone.find('.unitQuantity').attr('data-price', costprice);
    $clone.find('.stock_value').val(0);
    $clone.find('.variance_total').val(varianceTotal.toFixed(2));
    $clone.find('.variance').val(current_stock * -1);
    $clone.find('.variance').addClass('negative');
    $clone.css('display', '')

    var $lastRow = $('tbody#order-table-body tr').last();

    $lastRow.after($clone);

    // Handle SKU GUID - use appropriate field based on data type
    var skuGuid = isAutosaveData ? itemData.sku : itemData.sku_guid;
    if (skuGuid) {
        $('.sku[data-product="' + productId + '"]').val(skuGuid);
    }
}

$('body').on('click', '.delete-button', function () {
    var id = $(this).data('product');
    $('tr[data-product="' + id + '"]').remove()
});

$('body').on('change, blur, keyup', '.quantity', function () {
    var quantity = $(this).val();
    var productId = $(this).data('product');
    var skuPrice = $('[data-product="' + productId + '"].skuPrice').val()
    var price = skuPrice * quantity;

    $('.price[data-product="' + productId + '"]').val(price.toFixed(2))
});

$('body').on('change, blur', '.skuPrice', function () {
    var productId = $(this).data('product');
    var quantity = $('[data-product="' + productId + '"].quantity').val();
    var skuPrice = +$(this).val()
    $(this).val(skuPrice.toFixed(2))
    var price = skuPrice * quantity;

    $('.price[data-product="' + productId + '"]').val(price.toFixed(2))
});

var status = 0;

$(document).on('focus', '.quantity, .unitQuantity', function (e) {
    $(this).select();
});

$(document).on('keydown', '.quantity', function (e) {
    var keyCode = e.keyCode || e.which;

    if (keyCode == 9 || keyCode == 13) {
        e.preventDefault();
        var element = $(this).parent().parent().next().find('.quantity').focus()
    }
});
$(document).on('keydown', '.quantity', function (e) {
    var keyCode = e.keyCode || e.which;

    if (keyCode == 9) {
        e.preventDefault();
        var element = $(this).parent().parent().next().find('.quantity').focus()
    }
});

$(document).on('keydown', '.unitQuantity', function (e) {
    var keyCode = e.keyCode || e.which;

    if (keyCode == 9) {
        e.preventDefault();
        var element = $(this).parent().parent().next().find('.unitQuantity').focus()
    }
});


$("#stock-form").submit(function (e) {
    e.preventDefault();

    var $input = $('input.quantity, input.unitQuantity, input.stock_value');

    if (e.which != 9 && e.which != 13) {
        var form = this;
        var errors = false

        _.each($input, function (input) {
            if ($(input).val() == '') {
                notificationBar('error', 'All fields must have a value');
                $(input).focus();
                errors = true;
                return false;
            }
        });

        if (module === 'transfers') {
            let target = $('site_target').val();
            if (target === '') {
                notificationBar('error', 'Please select a site to transfer to.');
            }
        }

        if (module === 'requisitions') {
            let target = $('site_source').val();
            if (target === '') {
                notificationBar('error', 'Please select a site to request stock from.');
            }
        }

        if (!errors) {
            let newHtml = ''

            if (module === 'requisitions') {
                if (mode === 'create') {
                    $('#ConfirmStockModal .modal-body').html('By saving this stock requisition, it will be visible to the source site and they will need to update it to show when the requisition is complete.')

                    newHtml = '<button type="button" class="btn btn-danger pull-left" data-dismiss="modal">Cancel</button>' +
                        '<button type="submit" class="btn btn-warning" id="save-modal-button">Save</button>';
                } else {
                    $('#ConfirmStockModal .modal-body').html('You can save this stock requisition for later or complete it now, the stock will then be transferred to the requesting site.')

                    newHtml = '<button type="button" class="btn btn-danger pull-left" data-dismiss="modal">Cancel</button>' +
                        '<button type="submit" class="btn btn-warning" id="save-modal-button">Save</button>';

                    newHtml = newHtml + '<button type="submit" class="btn btn-success" id="confirm-modal-button">Complete</button>';

                }
            } else {
                $('#ConfirmStockModal .modal-body').html('If you would like to save your stock action for later, press <strong>save</strong> below. If you have completed your stock action' +
                    ' please press <strong>Complete</strong>. Once completed no further amendments can be made.')

                newHtml = '<button type="button" class="btn btn-danger pull-left" data-dismiss="modal">Cancel</button>' +
                    '<button type="submit" class="btn btn-warning" id="save-modal-button">Save</button>';

                if (module !== 'spotchecks') {
                    newHtml = newHtml + '<button type="submit" class="btn btn-success" id="confirm-modal-button">Complete</button>';
                }
                if (module === 'spotchecks' && companySpotcheckOption === 0) {
                    newHtml = newHtml + '<button type="submit" class="btn btn-success" id="confirm-modal-button">Complete</button>';
                }
            }

            $('#ConfirmStockModal .modal-footer').html(newHtml);
            $('#ConfirmStockModal').modal('show');

        }
    }
});

$('body').on('click', '#existing-order-items li', function () {
    var id = $(this).data('product');
    $('tr.item-record[data-product="' + id + '"]').remove()
    $(this).remove();
})

$(document).on('click', '#confirm-modal-button', function () {
    if (module == 'stocktake') {
        $('#ConfirmStockModal .modal-body').html('<div class="alert alert-danger text-center"><h3>Are you sure?</h3><br/>' +
            'Once complete, this stocktake can not be modified without reopening.</strong></div>')

        $('#ConfirmStockModal .modal-footer').html('<button type="button" class="btn btn-danger pull-left" data-dismiss="modal">Cancel</button>' +
            '<button type="submit" class="btn btn-success" id="confirm-stocktake-button">Complete</button>')

    } else {
        $('button').attr('disabled', 'true');
        complete(false)
    }
});

$(document).on('click', '#confirm-stocktake-button', function () {
    $('button').attr("disabled", true);
    complete(false);
});

function complete(reset) {
    reset = false;
    notificationBar('info', 'Saving your ' + module + ', please wait!', undefined, 'loading', true)
    // complete the stocktake
    $.ajax({
        type: "post",
        url: "/stock/" + module + "/create?status=4&reset=" + reset,
        data: $('form').serialize(),
        success: function (data) {
            deleteEntireHashMap();
            $('#ConfirmStockTakeModal').modal('hide');
            notificationBar(data.status, data.message, '/stock/' + module);
        }
    }).fail(function (data) {
        $('button').removeAttr("disabled");
        $('#ConfirmStockTakeModal').modal('hide');
        notificationBar(data.status, data.message)
    });
}

$(document).on('click', '#save-modal-button', function () {
    notificationBar('info', 'Saving your ' + module + ', please wait!', undefined, 'loading', true)
    $.ajax({
        type: "post",
        url: "/stock/" + module + "/create?status=0&reset=false",
        data: $('form').serialize(),
        success: function (data) {
            deleteEntireHashMap();
            $('#ConfirmStockTakeModal').modal('hide');
            notificationBar(data.status, data.message, '/stock/' + module + '/edit/' + data.id);
        }
    }).fail(function (data) {
        $('button').removeAttr("disabled");
        $('#ConfirmStockTakeModal').modal('hide');
        notificationBar(data.status, data.message)
    });
})

function switchAddTab(tab) {
    $('[data-type=add-tab]').hide();
    $('[data-type=add-tab].' + tab).show();
}

function difference(current_stock, new_stock) {
    if (current_stock < new_stock) {
        return Math.abs(current_stock - new_stock);
    }
    if (current_stock > new_stock) {
        return (Math.abs(current_stock - new_stock) * -1);
    }
    if (current_stock == new_stock) {
        return 0;
    }
}

$(document).ready(function () {
    $('#stock-form .multiselect').multiselect({
        buttonWidth: '100%',
        includeSelectAllOption: true,
        enableCaseInsensitiveFiltering: true
    });

});

$('#site').on('change', function () {

    var date = moment($('#stocktake_date').val(), 'DD/MM/YYYY H:mm:ss');
    date = date.format('DD/MM/YYYY H:mm:ss');

    var r = confirm("Changing the site will remove all the products from your stocktake!");
    if (r == true) {
        var target = $('.item-record').remove();
        delete target;

        var target2 = $('[colspan="11"]').remove();
        delete target2;

        getProducts($('#site').val(), date);
    } else {
        return false;
    }

});

var first = 0;
let initial = true;
let pathArray = window.location.pathname.split('/');
let stock_date = moment($('#stocktake_date').val(), 'DD/MM/YYYY H:mm:ss').format('YYYY-MM-DD H:mm:ss');
$("#datetimepicker1").on("dp.change", function (e) {
    if (initial !== true) {
        var date = moment($('#stocktake_date').val(), 'DD/MM/YYYY H:mm:ss');
        date = date.format('YYYY-MM-DD H:mm:ss');
        if (window.confirm("Changing this date will remove all the products from your stocktake!")) {
            window.location.href = '/stock/' + module + '/'+pathArray[3]+'/' + date;
        }
    } else {
        initial = false;
    }

    stock_date = moment($('#stocktake_date').val(), 'DD/MM/YYYY H:mm:ss').format('YYYY-MM-DD H:mm:ss');
});

